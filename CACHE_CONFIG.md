# 缓存配置说明

## 🎯 一键修改缓存默认位置

只需要修改一个地方就可以改变所有缓存的默认存储位置：

**文件**: `src/adapters/nodejs/storage.ts`

```typescript
// 🎯 DEFAULT CACHE LOCATION - Change this line to modify cache location globally
const DEFAULT_CACHE_BASE = os.homedir()  // Change to your preferred default location
```

## 示例修改

### 改为当前工作目录
```typescript
const DEFAULT_CACHE_BASE = process.cwd()
```

### 改为临时目录
```typescript
const DEFAULT_CACHE_BASE = os.tmpdir()
```

### 改为自定义目录
```typescript
const DEFAULT_CACHE_BASE = '/your/custom/cache/path'
```

### 改为项目根目录下的缓存文件夹
```typescript
const DEFAULT_CACHE_BASE = path.join(process.cwd(), '.cache')
```

## 工作原理

- 当用户没有显式指定 `cacheBasePath` 时，系统会使用 `DEFAULT_CACHE_BASE` 作为默认值
- 如果用户显式指定了 `cacheBasePath`，则使用用户指定的路径
- 实际的缓存文件会存储在 `{DEFAULT_CACHE_BASE}/workspaces/{workspace-hash}/` 目录下

## 注意事项

1. 修改后需要重新编译/重启应用
2. 确保指定的目录有读写权限
3. 不同的工作空间会在缓存基础目录下创建不同的子目录
