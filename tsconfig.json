{"compilerOptions": {"target": "es2020", "module": "es2020", "esModuleInterop": true, "downlevelIteration": true, "forceConsistentCasingInFileNames": true, "strict": true, "importHelpers": true, "noImplicitOverride": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noPropertyAccessFromIndexSignature": true, "declaration": false, "composite": false, "jsx": "react-jsx", "allowSyntheticDefaultImports": true, "moduleResolution": "bundler", "skipLibCheck": true}, "include": ["src/**/*"], "exclude": ["vite.config.ts", "vite.config.mts", "vitest.config.ts", "vitest.config.mts", "**/__tests__/**/*", "src/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.test.tsx", "src/**/*.spec.tsx", "src/**/*.test.js", "src/**/*.spec.js", "src/**/*.test.jsx", "src/**/*.spec.jsx", "dist/**/*", "node_modules/**/*"]}