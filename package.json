{"name": "@autodev/codebase", "version": "0.0.4", "type": "module", "bin": {"codebase": "./dist/cli.js"}, "files": ["dist/**/*"], "scripts": {"dev": "rm -rf .autodev-cache/ && npx tsx src/index.ts --demo", "build": "rollup -c rollup.config.cjs && chmod +x dist/cli.js", "type-check": "npx tsc -p tsconfig.json --noEmit", "demo-tui": "npx tsx src/examples/run-demo-tui.tsx", "mcp-server": "npx tsx src/index.ts mcp-server --demo --port=3002", "push": "npm publish --access public"}, "peerDependencies": {"ink": "^4.4.1", "react": "^18.3.1", "vscode": "^1.74.0"}, "peerDependenciesMeta": {"vscode": {"optional": true}, "react": {"optional": true}, "ink": {"optional": true}}, "dependencies": {"@modelcontextprotocol/sdk": "^1.13.1", "@qdrant/js-client-rest": "^1.11.0", "@types/ink": "^2.0.3", "async-mutex": "^0.5.0", "csstype": "^3.1.3", "form-data": "^4.0.3", "fzf": "^0.5.2", "ignore": "^5.3.1", "ink": "^4.4.1", "lodash.debounce": "^4.0.8", "openai": "^4.52.0", "p-limit": "^3.1.0", "react": "^18.3.1", "tree-sitter": "^0.21.1", "tree-sitter-wasms": "^0.1.12", "tslib": "^2.7.0", "undici": "^6.19.8", "undici-types": "^7.10.0", "uuid": "^10.0.0", "vitest": "^3.2.4", "web-tree-sitter": "^0.23.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^26.0.1", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.6", "@types/express": "^5.0.3", "@types/lodash.debounce": "^4.0.9", "@types/react": "^18.3.23", "@types/uuid": "^10.0.0", "@types/vscode": "^1.101.0", "rollup": "^4.21.2", "tsx": "^4.20.3", "typescript": "^5.6.2", "vscode": "^1.1.37"}}