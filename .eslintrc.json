{"extends": ["../../.eslintrc.json"], "ignorePatterns": ["!**/*", "**/vite.config.*.timestamp*", "**/vitest.config.*.timestamp*"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {}}, {"files": ["*.ts", "*.tsx"], "rules": {}}, {"files": ["*.js", "*.jsx"], "rules": {}}, {"files": ["*.json"], "parser": "jsonc-eslint-parser", "rules": {"@nx/dependency-checks": ["error", {"ignoredFiles": ["{projectRoot}/eslint.config.{js,cjs,mjs}", "{projectRoot}/rollup.config.{js,ts,mjs,mts,cjs,cts}", "{projectRoot}/vite.config.{js,ts,mjs,mts}"]}]}}]}